/**
 * @format
 */

// Intl polyfill for React Native
if (!global.Intl) {
  global.Intl = {};
}

// NumberFormat polyfill
if (!global.Intl.NumberFormat) {
  function NumberFormat(locale, options) {
    options = options || {};
    
    return {
      format: function(number) {
        if (number === undefined || number === null) return '';
        
        const num = Number(number);
        if (isNaN(num)) return '';
        
        if (options.style === 'currency') {
          const fractionDigits = options.minimumFractionDigits !== undefined
            ? options.minimumFractionDigits
            : 0;
          const formatted = Math.abs(num).toFixed(fractionDigits);
          const parts = formatted.split('.');
          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          const result = parts.join('.');
          const symbol = options.currency === 'USD' ? '$' : '$';
          return (num < 0 ? '-' : '') + symbol + result;
        }
        
        // For non-currency formatting, add commas for thousands
        const formatted = Math.abs(num).toString();
        const parts = formatted.split('.');
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        return (num < 0 ? '-' : '') + parts.join('.');
      }
    };
  }
  global.Intl.NumberFormat = NumberFormat;
}

// DateTimeFormat polyfill  
if (!global.Intl.DateTimeFormat) {
  function DateTimeFormat(locale, options) {
    options = options || {};
    
    return {
      format: function(date) {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        if (options.year && options.month && options.day) {
          const month = String(d.getMonth() + 1).padStart(2, '0');
          const day = String(d.getDate()).padStart(2, '0');
          const year = d.getFullYear();
          return `${month}/${day}/${year}`;
        }
        
        // Default formatting
        return d.toLocaleDateString();
      }
    };
  }
  global.Intl.DateTimeFormat = DateTimeFormat;
}

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

AppRegistry.registerComponent(appName, () => App);
