# ninja log v5
15	913	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/x86_64/CMakeFiles/cmake.verify_globs	f0c77e409392a71a
115	46888	7785974313957881	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	bfc922150f070072
61	49038	7785974332294508	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	99f02ffc703e4acc
88	52684	7785974359069458	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	8788d560fd9ae819
49	54600	7785974389031606	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	7ab222aba04819e1
103	54709	7785974390647197	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	34e5e2655c40904b
24	60707	7785974451608068	CMakeFiles/appmodules.dir/OnLoad.cpp.o	d0dd9936f4b5ce32
75	66732	7785974501949749	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	13d9a2eb862db3ee
37	67064	7785974512427429	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	9bfd25017f4bb0a6
54683	81735	7785974662386108	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	91d53df5fc51a346
126	82455	7785974668848379	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	87720d8fc10c57a6
46901	83315	7785974669001636	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	cfe5fa14a20ae693
60709	85662	7785974700505596	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	d86d4c2017193573
52880	87580	7785974704704235	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	ba9400ffb790c64
54710	91662	7785974751010069	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	22e81bda5820f8e2
49040	92962	7785974759735810	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	e83f7d8e6a9276a9
83315	101729	7785974861269322	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e2ed75149ca39ba1b90212a2fe4317a4/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	23a096bb2a942829
67044	103957	7785974877553058	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	fd64eead3f050db9
67064	105789	7785974901264615	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e2ed75149ca39ba1b90212a2fe4317a4/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	a1e4461c40bb3931
82456	106405	7785974909395927	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/dcd08aa91023519d84d91693d9f1f1d8/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	81ee30601cd431b6
81736	107661	7785974919948018	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	1149081378c941ac
91662	109019	7785974935559851	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/States.cpp.o	d34cb7af33788f62
87580	115545	7785975000093365	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/RNLocalizeSpec-generated.cpp.o	a7872994a96184ed
92962	117388	7785975018891615	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ShadowNodes.cpp.o	d0564b7f186beeca
85662	119743	7785975039505485	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	df74fee6af2f9d21
5	121070	7785975050555454	CMakeFiles/appmodules.dir/D_/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	76adaa4beb22d6fa
101730	122953	7785975073252750	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/RNLocalizeSpecJSI-generated.cpp.o	e345c9b0a7a4cc61
119744	123084	7785975070877519	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/x86_64/libreact_codegen_rnpicker.so	1427072ad69116a6
103958	127853	7785975123667069	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/EventEmitters.cpp.o	49f1ee339357dfb5
106406	128483	7785975129943101	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/Props.cpp.o	594d9f7605abe28b
107661	133365	7785975177988240	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46385401038c71fe263b0361349646db/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	32a435387cd21ca5
109020	135332	7785975197086771	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0d8ae10feccd0f6526f263a13f784a18/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	6d3e76e11e50f4b7
105790	137025	7785975206448541	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ComponentDescriptors.cpp.o	4a059fdd668303b1
122955	143482	7785975279163349	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0d8ae10feccd0f6526f263a13f784a18/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	27e425b4ebaf3d41
115545	147483	7785975314804637	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/748642ad7f4e81cc696a29595dcf082c/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	3a54864af040af66
121072	149168	7785975331630457	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/808ee6d34b954a992d23153c3c974363/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	5a40ac7dc0f1ada9
123085	150785	7785975346413589	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2bbca0bbce388ad51babb96f32ffd60d/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	6d770bf01d4fdb2d
128484	150970	7785975352336421	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdfa3f9b2b67d0afe3ba4995806d9f6b/components/safeareacontext/safeareacontextJSI-generated.cpp.o	73887107ba57eb19
117389	152714	7785975371120808	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d2768da2c41093e051b467ea24978391/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	db418049d63cf0a9
127854	161179	7785975450211377	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46385401038c71fe263b0361349646db/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	54840c68294a0d80
133366	161417	7785975451836909	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	c45fc24c1699d21a
161266	164998	7785975488715042	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/x86_64/libreact_codegen_safeareacontext.so	9ca6680791624f76
137026	171009	7785975546887808	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	497c339b537b04a
135334	172463	7785975561876206	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	cdc56b90cf18def6
143483	176238	7785975601454546	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	192cf50bd3a92e0e
150786	178134	7785975625784751	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	792815f2b8dfc17a
149288	178205	7785975625790463	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	f8b541510ab94b82
147484	179482	7785975639309658	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	4a294a1927f36d20
152716	179870	7785975641568417	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	b509924dae7ee2dd
151016	181404	7785975659007445	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c974b5ba5572e66
161418	188105	7785975726039015	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/76332b4bb1b959fae2eba352e685d68e/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	b39bbfd2c6cafe11
172465	189181	7785975734980873	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8193e40b1a787f6c465dee887f909f9c/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	ef73d251cf549f45
164998	189526	7785975738579242	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	58c028e56723f0b6
171158	195370	7785975798717090	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/47507ae0ab1d3ecbc75f4f7cec0496c4/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	9398132be81ea26f
180017	198337	7785975828175228	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b2fac415ed4cf67f3452476fd8f694ac/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	a61e64f7dc200e9b
176685	206257	7785975899620461	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	d86e90462b808e79
179484	212805	7785975961963785	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8193e40b1a787f6c465dee887f909f9c/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	14a72715ad63bc8a
178135	218386	7785976024290800	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/47507ae0ab1d3ecbc75f4f7cec0496c4/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	941a256a85eaa71
178323	237719	7785976209834124	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b2fac415ed4cf67f3452476fd8f694ac/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	2cd7ada57866b7db
237723	240167	7785976242758060	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/x86_64/libreact_codegen_rnscreens.so	782f80f88500778d
240167	243066	7785976273116218	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/x86_64/libappmodules.so	a329927b48e1285
28	701	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/x86_64/CMakeFiles/cmake.verify_globs	f0c77e409392a71a
23	123	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/x86_64/CMakeFiles/cmake.verify_globs	f0c77e409392a71a
