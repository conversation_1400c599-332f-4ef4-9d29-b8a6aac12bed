[{"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDialogPickerMeasurementsManager.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerMeasurementsManager.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerMeasurementsManager.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDialogPickerShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDialogPickerState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDropdownPickerMeasurementsManager.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerMeasurementsManager.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerMeasurementsManager.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDropdownPickerShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDropdownPickerState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\rnpicker.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\rnpicker.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\rnpicker.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\7608099fe76eac2b9dc3e3b9420f25f6\\jni\\react\\renderer\\components\\rnpicker\\ComponentDescriptors.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ComponentDescriptors.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ComponentDescriptors.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\dcd08aa91023519d84d91693d9f1f1d8\\codegen\\jni\\react\\renderer\\components\\rnpicker\\EventEmitters.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\EventEmitters.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\EventEmitters.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\a979fb418e3faff683f9ee813a12f33a\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\Props.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\Props.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\Props.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\dcd08aa91023519d84d91693d9f1f1d8\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ShadowNodes.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ShadowNodes.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ShadowNodes.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\a979fb418e3faff683f9ee813a12f33a\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\States.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\States.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\States.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnpicker_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\7608099fe76eac2b9dc3e3b9420f25f6\\jni\\react\\renderer\\components\\rnpicker\\rnpickerJSI-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\rnpickerJSI-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\rnpickerJSI-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\RNLocalizeSpec-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\RNLocalizeSpec-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\RNLocalizeSpec-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\react\\renderer\\components\\RNLocalizeSpec\\ComponentDescriptors.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\ComponentDescriptors.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\react\\renderer\\components\\RNLocalizeSpec\\EventEmitters.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\EventEmitters.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\EventEmitters.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\react\\renderer\\components\\RNLocalizeSpec\\Props.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\Props.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\Props.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\react\\renderer\\components\\RNLocalizeSpec\\RNLocalizeSpecJSI-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\RNLocalizeSpecJSI-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\RNLocalizeSpecJSI-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\react\\renderer\\components\\RNLocalizeSpec\\ShadowNodes.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\ShadowNodes.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\ShadowNodes.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNLocalizeSpec_autolinked_build\\CMakeFiles\\react_codegen_RNLocalizeSpec.dir\\react\\renderer\\components\\RNLocalizeSpec\\States.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\States.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNLocalizeSpec\\States.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\2008ebd19feb76ded482342493bc0010\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\748642ad7f4e81cc696a29595dcf082c\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\bdfa3f9b2b67d0afe3ba4995806d9f6b\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\bdfa3f9b2b67d0afe3ba4995806d9f6b\\components\\safeareacontext\\EventEmitters.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\70edacbeee98a952d7798c4ba831ae93\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\d2768da2c41093e051b467ea24978391\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\70edacbeee98a952d7798c4ba831ae93\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3e7e853c946e3f21f9e9e896a664ab32\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\04fca16af4ebaaa72fdf2d8029a87062\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\76332b4bb1b959fae2eba352e685d68e\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a42b92bac6366714a1977bd6f5a4c16c\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6a7e421a73347c7dffc180b75d998cd4\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\76332b4bb1b959fae2eba352e685d68e\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a42b92bac6366714a1977bd6f5a4c16c\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0c06354ce04fefa943c23b0c768e6cf8\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0c06354ce04fefa943c23b0c768e6cf8\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0c06354ce04fefa943c23b0c768e6cf8\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0c06354ce04fefa943c23b0c768e6cf8\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a42b92bac6366714a1977bd6f5a4c16c\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6a7e421a73347c7dffc180b75d998cd4\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\801f8009a592cfedcc170279b3759d76\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\801f8009a592cfedcc170279b3759d76\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\47507ae0ab1d3ecbc75f4f7cec0496c4\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b2fac415ed4cf67f3452476fd8f694ac\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b2fac415ed4cf67f3452476fd8f694ac\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/. -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/3cec8c5b1a3c8047758b5cc888b1af12/transformed/react-android-0.80.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -Werror -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\2a265d105876c5448114b95f755cd567\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}]