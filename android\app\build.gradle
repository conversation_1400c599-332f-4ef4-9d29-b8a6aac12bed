apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

/**
 * React Native configuration
 */
react {
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * JavaScriptCore (JSC) flavor
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'

/**
 * Load keystore properties
 */
def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    ndkVersion = rootProject.ext.ndkVersion
    buildToolsVersion = rootProject.ext.buildToolsVersion
    compileSdkVersion = rootProject.ext.compileSdkVersion

    namespace = "com.tradeworks"

    aaptOptions {
        cruncherEnabled = false
        useNewCruncher = false
    }

    defaultConfig {
        applicationId = "com.tradeworks"
        minSdkVersion = rootProject.ext.minSdkVersion
        targetSdkVersion = rootProject.ext.targetSdkVersion
        versionCode = 1
        versionName = "1.0"
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (keystorePropertiesFile.exists()) {
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
            }
        }
    }

    buildTypes {
        debug {
            signingConfig = signingConfigs.debug
        }
        release {
            signingConfig = signingConfigs.release
            minifyEnabled = enableProguardInReleaseBuilds
            shrinkResources = false
            debuggable = false
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
        }
    }
}

dependencies {
    implementation("com.facebook.react:react-android")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}
