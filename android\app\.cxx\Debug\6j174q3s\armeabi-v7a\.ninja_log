# ninja log v5
65966	97866	7785922872224930	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/801f8009a592cfedcc170279b3759d76/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	668d0120374884d1
18	181	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs	310ccfcd905d81fa
177	15591	7785916450123295	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	222f55fcaeddba92
58482	75687	7785922652292725	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/76332b4bb1b959fae2eba352e685d68e/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	5724deb0775c1849
46238	62111	7785922516963003	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/70edacbeee98a952d7798c4ba831ae93/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	165afd550f1ac4af
97	16289	7785916458661079	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	92a9fa42b2a6c385
61	21708	7785916510578665	CMakeFiles/appmodules.dir/OnLoad.cpp.o	abb61a49911559f7
38099	55347	7785922444548226	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/841c34f75a066d706098afe80dac2de1/generated/source/codegen/jni/safeareacontext-generated.cpp.o	3af3bf1d6bf750e9
80	21807	7785916511982333	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	10726593ea75a639
159	19402	7785916489696056	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	d317ae8b6851ad7f
145	20555	7785916500363982	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	d651d634dea9f3
20594	34021	7785922235662181	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/EventEmitters.cpp.o	6d1729ca9543af69
43771	54708	7785922441315675	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46385401038c71fe263b0361349646db/jni/react/renderer/components/safeareacontext/States.cpp.o	861c5f333831d716
113	20998	7785916503398123	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	25dcff4282fbc497
14475	28433	7785922179625024	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	5b62c01cb17aa378
215	21239	7785916507407811	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	5a91ece33641bd75
46	20264	7785922097023620	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	de49cfb8ceb1e8a2
79	14475	7785922040180457	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	52d3b122ec9b2c6b
113	14383	7785922039550373	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	4ff21e6d26b8b0c3
22138	35638	7785922249196152	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/RNLocalizeSpec-generated.cpp.o	ebd21d4b31ab3976
125	20593	7785922098509235	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	8fb30e10f053af6a
19851	30759	7785922203239685	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	fb84e8414d30a92c
59	19851	7785922093694817	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	3b3936faf2f0eb2d
91	20786	7785922103410796	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	7878a5af661002a4
21313	37451	7785922268174483	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/RNLocalizeSpecJSI-generated.cpp.o	b290e045c4ec914a
71627	91437	7785922808025734	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/47507ae0ab1d3ecbc75f4f7cec0496c4/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	9c493a5bbac22ed2
102	21628	7785922111726817	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/dcd08aa91023519d84d91693d9f1f1d8/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	f25347e8341d53a5
31926	47295	7785922367997844	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/70edacbeee98a952d7798c4ba831ae93/react/renderer/components/safeareacontext/EventEmitters.cpp.o	bbf1aacf3fba988c
69	21312	7785922108121206	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	5ee287c4f7bbe586
64221	88620	7785922780907034	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b2fac415ed4cf67f3452476fd8f694ac/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	616a32e810bfac44
20342	34534	7785922240785112	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/dcd08aa91023519d84d91693d9f1f1d8/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	c5e01c41f3fc6894
135	22137	7785922116699685	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e2ed75149ca39ba1b90212a2fe4317a4/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	47025b10277dfd25
37452	52895	7785922424868198	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdfa3f9b2b67d0afe3ba4995806d9f6b/components/safeareacontext/safeareacontextJSI-generated.cpp.o	1eb0eeadc4af3adc
14386	31832	7785922206982866	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	93142f2537d0aed4
35	48233	7785922372524381	CMakeFiles/appmodules.dir/D_/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	b37c05fc0a1af2f8
35725	52992	7785922425250658	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/748642ad7f4e81cc696a29595dcf082c/components/safeareacontext/RNCSafeAreaViewState.cpp.o	2846da2166d0bdfd
30760	43770	7785922332251430	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/States.cpp.o	58c35ed9b7d9f109
52993	65966	7785922555514332	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	65581bb07b3e7806
34535	37074	7785922263481880	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/armeabi-v7a/libreact_codegen_rnpicker.so	ab33f6a34e42e2ca
28433	46237	7785922358344924	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ShadowNodes.cpp.o	fd0381a017360734
47563	63445	7785922530536498	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	97af67a5bfa98639
21629	38099	7785922277090164	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ComponentDescriptors.cpp.o	ed4551e841fb4e09
20787	36886	7785922264341774	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/Props.cpp.o	79bea2412323d518
34021	52329	7785922417113547	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46385401038c71fe263b0361349646db/jni/react/renderer/components/safeareacontext/Props.cpp.o	1e77b8482f20c552
71757	83123	7785922727241727	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/801f8009a592cfedcc170279b3759d76/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	fc0204335d8826b1
36887	58482	7785922477380763	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/748642ad7f4e81cc696a29595dcf082c/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	64427db759694657
37075	58191	7785922474093631	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdfa3f9b2b67d0afe3ba4995806d9f6b/components/safeareacontext/ComponentDescriptors.cpp.o	4345a83fc09335da
52897	71509	7785922607246522	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/76332b4bb1b959fae2eba352e685d68e/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	71436831fbc2ead8
62112	64252	7785922534217951	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	530c0ea7568532f2
54709	72843	7785922621596376	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	e1006f2741656451
66759	85148	7785922745984582	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	6ebfbbc869b5cdd7
55348	72862	7785922622848136	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	3613a7dceefa7df8
52329	64218	7785922537774773	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/76332b4bb1b959fae2eba352e685d68e/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	2d23e0a8b67fab98
48321	66759	7785922563358486	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	7f45f4be352bd59c
63446	80163	7785922697713018	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	bbf91bff6c318e0f
58291	71756	7785922613513453	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e2aa3672a5877344f44e30dc4122bced/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	bcff5ce20a1176bf
64253	77470	7785922669509121	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	8aecdd06bf9fe4ce
72862	82987	7785922725739340	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/47507ae0ab1d3ecbc75f4f7cec0496c4/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	9fd1e7e1a2323365
72843	86069	7785922754298644	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/47507ae0ab1d3ecbc75f4f7cec0496c4/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	8dd7fdd06c0d4439
97867	98372	7785922879111812	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/armeabi-v7a/libreact_codegen_rnscreens.so	850e8421696721d0
98374	99038	7785922885104742	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/armeabi-v7a/libappmodules.so	105f732e3515012b
182	4937	7785969969053362	build.ninja	59249e1567de3823
1	316	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs	310ccfcd905d81fa
0	76	0	clean	421738e464f0a43e
