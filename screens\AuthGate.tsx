import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ActivityIndicator, View } from 'react-native';
import { getToken } from './token';
import { loginSuccess, logout } from '../store';
import SignIn from './SignIn';
import JoinUs from './JoinUs';
import LocationScreen from './LocationScreen';
import CompanionScreen from './CompanionScreen';
import JobListScreen from './JobListScreen';
import ProfileDropdown from '../components/ProfileDropdown';
import JobDetailScreen from './JobDetailScreen';
import EasyApply from './EasyApply';
import EditContactInfo from './EditContactInfo';

import type { JobData } from '../navigation/types';

type RootStackParamList = {
  SignIn: undefined;
  JoinUs: undefined;
  Location: undefined;
  Companion: undefined;
  JobListScreen: undefined;
  JobDetailScreen: { job: JobData };
  EasyApply: undefined;
  EditContactInfo: { from: string };
};

const Stack = createNativeStackNavigator<RootStackParamList>();


const AuthGate = () => {
  const dispatch = useDispatch();
  const { token } = useSelector((state: any) => state.user);
  const [loading, setLoading] = React.useState(true);

  useEffect(() => {
    const checkToken = async () => {
      try {
        const storedToken = await getToken();
        console.log('Token from storage:', storedToken); // Debug log
        if (storedToken) {
          dispatch(loginSuccess({ user: {}, token: storedToken }));
        } else {
          dispatch(logout()); // Clear any stale state if no token
        }
      } catch (error) {
        console.error('Error checking token:', error);
        dispatch(logout()); // Clear any stale state on error
      } finally {
        setLoading(false);
      }
    };
    checkToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Wait for loading and for token to be set in Redux before rendering navigation
  if (loading || (typeof token === 'undefined')) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#410166" />
      </View>
    );
  }

  const handleLogout = async (
    navigation: NativeStackNavigationProp<RootStackParamList, any>
  ) => {
    await dispatch(logout());
    navigation.reset({ index: 0, routes: [{ name: 'SignIn' }] });
  };

  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName={token ? 'JobListScreen' : 'SignIn'} screenOptions={{ headerShown: true }}>
        <Stack.Screen name="SignIn" component={SignIn} />
        <Stack.Screen name="JoinUs" component={JoinUs} />
        <Stack.Screen name="Location" component={LocationScreen} />
        <Stack.Screen name="Companion" component={CompanionScreen} />
        <Stack.Screen 
          name="JobListScreen" 
          component={JobListScreen} 
          options={({ navigation }) => ({
            title: 'Jobs',
            headerBackVisible: false, // Hide the back button on JobListScreen
            headerRight: () => <ProfileDropdown onLogout={() => handleLogout(navigation)} />, 
          })}
        />

        <Stack.Screen name="JobDetailScreen" component={JobDetailScreen} options={{ title: 'Job Details' }} />
        <Stack.Screen name="EasyApply" component={EasyApply} options={{ title: 'Easy Apply' }} />
        <Stack.Screen name="EditContactInfo" component={EditContactInfo} options={{ title: 'Edit Contact Info' }} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AuthGate;
