# Manual Keystore Generation Guide

Since Java isn't installed, here are alternative methods:

## Method 1: Install Java JDK
1. Download from: https://adoptium.net/temurin/releases/
2. Install the Windows x64 MSI package
3. Restart terminal and run: `./generate-keystore.bat`

## Method 2: Use Android Studio
1. Open Android Studio
2. Build → Generate Signed Bundle / APK
3. Create new keystore
4. Save as: `android/app/my-upload-key.keystore`
5. Remember your passwords!

## Method 3: Use Existing React Native Setup
If you can build React Native, Java is likely installed but not in PATH:

1. Check if Android Studio has Java:
   - Look in: `C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe`
   
2. Use full path:
```bash
"C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe" -genkeypair -v -storetype PKCS12 -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

## After Creating Keystore:
Update `android/gradle.properties`:
```
MYAPP_UPLOAD_STORE_FILE=my-upload-key.keystore
MYAPP_UPLOAD_KEY_ALIAS=my-key-alias
MYAPP_UPLOAD_STORE_PASSWORD=your_password
MYAPP_UPLOAD_KEY_PASSWORD=your_password
```
