# ninja log v5
34	199	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/armeabi-v7a/CMakeFiles/cmake.verify_globs	3f3e95ed8e85c239
129621	157310	7785411270900641	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b2fac415ed4cf67f3452476fd8f694ac/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	7eaa5da24cdae73f
77	28686	7785409985319669	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	3775ebc897c9ffc6
106431	127133	7785410965391091	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	faaac6af5988f80b
5	41939	7785410117871076	CMakeFiles/appmodules.dir/OnLoad.cpp.o	f8a0b2fe34aaea8c
32	32121	7785410004622393	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	e7fa2c698305880b
19	38907	7785410086330918	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	a258b2f2a3d24297
124141	166510	7785411360880990	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2a265d105876c5448114b95f755cd567/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	48b071c271a7fbbb
92174	112961	7785410819090037	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d2768da2c41093e051b467ea24978391/renderer/components/safeareacontext/States.cpp.o	8ef6cd30dce7b8ed
79827	95087	7785410644306165	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdfa3f9b2b67d0afe3ba4995806d9f6b/components/safeareacontext/EventEmitters.cpp.o	b3a96ff650185f2e
60	36501	7785410062924520	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	d7d913f3146a3b28
89185	109753	7785410793582966	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/04fca16af4ebaaa72fdf2d8029a87062/source/codegen/jni/safeareacontext-generated.cpp.o	badb05f452130f08
104	41780	7785410110999968	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	6bc22f46d0bf2d1a
113637	130432	7785411001037897	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	4e7f726929bc36a
41939	85808	7785410554032507	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	f3f913e8bd852a60
90	41473	7785410110435241	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	d56c177f468f14c7
76824	92118	7785410613222202	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/EventEmitters.cpp.o	82e615591250b62d
44	40487	7785410101252591	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	cae6dc5b528408bd
119	53432	7785410218754041	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	a7de1f16c3e32e5d
328	60032	7785410298304972	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	762417f04197a96b
40488	79680	7785410489193208	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/7608099fe76eac2b9dc3e3b9420f25f6/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	6290048d1e92de62
28862	63610	7785410327871939	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	1c89f8f1643da986
112962	135622	7785411046905075	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/76332b4bb1b959fae2eba352e685d68e/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	62476989ad59de87
63630	83477	7785410533768955	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/RNLocalizeSpec-generated.cpp.o	e8379a0bfaa8296f
38909	63630	7785410328133006	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	9df6aed418845557
32122	77711	7785410474217883	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	4b1a6c28d14a85c3
53434	67188	7785410363936580	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/a979fb418e3faff683f9ee813a12f33a/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	d57af3375e291557
77058	90738	7785410599859392	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ShadowNodes.cpp.o	e67333f1f8cd9135
41781	68790	7785410385566703	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/dcd08aa91023519d84d91693d9f1f1d8/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	debb2b0b8c11370f
80000	107151	7785410770677109	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2008ebd19feb76ded482342493bc0010/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	2cb51be524a6aae8
36502	76823	7785410462174500	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	92255c85e724d999
77496	93877	7785410628964103	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/RNLocalizeSpecJSI-generated.cpp.o	56c973eb0523119f
41474	77058	7785410463956419	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	386fff3c00b3fd34
166511	167767	7785411373879872	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/RelWithDebInfo/y3560144/obj/armeabi-v7a/libreact_codegen_rnscreens.so	f4a7bef09fe48445
133760	147259	7785411171867030	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2a265d105876c5448114b95f755cd567/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	1cf57e1296d0a36d
67189	77495	7785410474485686	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/States.cpp.o	3bbf5c60f788ccbd
60032	79999	7785410497333266	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/7608099fe76eac2b9dc3e3b9420f25f6/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	5a442ba0f8ed02f9
68791	82716	7785410519306001	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/Props.cpp.o	443107469f83b237
63610	84303	7785410540305782	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/dcd08aa91023519d84d91693d9f1f1d8/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	631b2ef9fad08f1d
85811	89185	7785410590109180	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/RelWithDebInfo/y3560144/obj/armeabi-v7a/libreact_codegen_rnpicker.so	699c0dcabda34ab2
77711	96412	7785410663730700	RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ComponentDescriptors.cpp.o	1bede2f9f6ce02fd
84309	106024	7785410758814580	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2008ebd19feb76ded482342493bc0010/safeareacontext/RNCSafeAreaViewState.cpp.o	f2f1a31fd91228c4
83477	106430	7785410763330434	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d2768da2c41093e051b467ea24978391/renderer/components/safeareacontext/Props.cpp.o	b05ccabd1bb06559
82717	113342	7785410824376106	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3e7e853c946e3f21f9e9e896a664ab32/safeareacontext/ComponentDescriptors.cpp.o	e4a939da0ad1fb13
90739	113635	7785410827248944	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3e7e853c946e3f21f9e9e896a664ab32/safeareacontext/safeareacontextJSI-generated.cpp.o	103bc61781fc8e18
95092	116249	7785410845125406	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a42b92bac6366714a1977bd6f5a4c16c/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	97a907d45a61861f
93878	116653	7785410859790847	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdfa3f9b2b67d0afe3ba4995806d9f6b/components/safeareacontext/ShadowNodes.cpp.o	5d82e28298d9b781
96413	120040	7785410897967369	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0c06354ce04fefa943c23b0c768e6cf8/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	f3a6a7a2de645c0b
116654	120415	7785410892686035	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/RelWithDebInfo/y3560144/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	42639525d3e5d96b
124992	137592	7785411068221601	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b2fac415ed4cf67f3452476fd8f694ac/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	7ae15509e938cd71
106026	124084	7785410933122324	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0c06354ce04fefa943c23b0c768e6cf8/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	5b1977ec19ce45d2
107152	124991	7785410943816032	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0c06354ce04fefa943c23b0c768e6cf8/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	888e504304eaa99e
109754	129621	7785410994077800	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0c06354ce04fefa943c23b0c768e6cf8/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	4a3c5ebfe79eaea1
113416	133621	7785411032002325	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	f701a6e53d8665a3
116451	133901	7785411033077016	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6a7e421a73347c7dffc180b75d998cd4/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	c756c84168189d2f
130434	158542	7785411283464452	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/801f8009a592cfedcc170279b3759d76/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	81e63cb217bca855
120416	138459	7785411080275208	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/801f8009a592cfedcc170279b3759d76/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	5b8fc1d567ed2309
120202	138595	7785411085024832	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0c06354ce04fefa943c23b0c768e6cf8/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	b02ee39711a47d5c
127137	146008	7785411158394208	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	2ad3e872bf5f8083
133902	192226	7785411620621468	CMakeFiles/appmodules.dir/D_/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	9cc971af9f483707
192227	192642	7785411625888915	D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/RelWithDebInfo/y3560144/obj/armeabi-v7a/libappmodules.so	c66a284c5824a8d2
200	4632	7785970073218008	build.ninja	1231931673215345
1	464	0	D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/armeabi-v7a/CMakeFiles/cmake.verify_globs	3f3e95ed8e85c239
0	247	0	clean	421738e464f0a43e
