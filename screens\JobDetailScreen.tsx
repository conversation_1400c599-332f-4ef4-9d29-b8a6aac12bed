import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  useWindowDimensions,
} from 'react-native';
import RenderHTML from 'react-native-render-html';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import JobDetailScreenStyles from './styles/JobDetailScreenStyles';
import type { RootStackParamList, JobData } from '../navigation/types';


type RouteParams = {
  job: JobData;
};

const returnSalaryRange = (salary: string) => {
  switch (salary) {
    case 'Hourly':
      return 2;
    case 'Daily':
      return 12;
    case 'Weekly':
      return 250;
    case 'Monthly':
      return 600;
    case 'Yearly':
      return 3000;
    default:
      return 2;
  }
};

const getFormattedCurrency = (salary: number, jobDuration: string) => {
  if (!salary) {
    return '';
  }
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(salary * returnSalaryRange(jobDuration));
};

//returns formatted date in US
const getPreviewDate = (date: string) => {
  if (!date) {
    return '';
  }
  const NewDt = new Date(date);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(NewDt);
};

const formatTime = (postedDate: string) => {
  if (!postedDate) {
    return 'Recently posted';
  }
  
  try {
    let differenceTime = new Date(postedDate).getTime() - new Date().getTime();
    let roundedTime = Math.round(differenceTime / 1000 / 60 / 60);
    if (roundedTime === 1 || roundedTime === 0) {
      return 'Posted less than an hour ago';
    } else if (roundedTime === 2) {
      return 'Posted 2 hours ago';
    } else if (roundedTime > 2 && roundedTime <= 5) {
      return 'Posted 5 hours ago';
    } else if (roundedTime > 5 && roundedTime <= 12) {
      return 'Posted 12 hours ago';
    } else if (roundedTime > 12 && roundedTime <= 24) {
      return 'Posted 1 day ago';
    } else {
      return `Posted ${getPreviewDate(postedDate)}`;
    }
  } catch (error) {
    console.warn('Error formatting time:', error);
    return 'Recently posted';
  }
};

const returnJobDreamIteams = (job: any) => {
  const matches =
    job.dreamJobInfo?.configValue?.filter(
      (item: any) =>
        item.isCompleted && item.userImportanceRating !== 0 && item.display,
    ) || [];
  const notMatches =
    job.dreamJobInfo?.configValue?.filter(
      (item: any) =>
        !item.isCompleted && item.userImportanceRating !== 0 && item.display,
    ) || [];

  return (
    <View style={{ flexDirection: 'row', width: '100%' }}>
      {/* Matches on the left */}
      <View style={{ flex: 1, paddingRight: 8 }}>
        {matches.length === 0 && (
          <Text style={{ color: '#888' }}>No matches</Text>
        )}
        {matches.map((item: any, idx: number) => (
          <View
            key={idx}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 6,
            }}
          >
            <Text style={{ color: '#3cbb4a', fontSize: 18, marginRight: 6 }}>
              ✔️
            </Text>
            <Text style={{ textTransform: 'capitalize', fontSize: 14 }}>
              {item.title || item.userAttributes}
            </Text>
          </View>
        ))}
      </View>
      {/* Not matches on the right */}
      <View style={{ flex: 1, paddingLeft: 8 }}>
        {notMatches.length === 0 && (
          <Text style={{ color: '#888' }}>No non-matches</Text>
        )}
        {notMatches.map((item: any, idx: number) => (
          <View
            key={idx}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 6,
            }}
          >
            <Text style={{ color: '#e14d4d', fontSize: 18, marginRight: 6 }}>
              ✗
            </Text>
            <Text style={{ textTransform: 'capitalize', fontSize: 14 }}>
              {item.title || item.userAttributes}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const JobDetailScreen = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  
  // Safe extraction of job data with fallback
  const job = (route.params as any)?.job as JobData;
  
  React.useEffect(() => {
    console.log('JobDetailScreen', { hasJob: !!job });
  }, [job]);

  // Early return if no job data to prevent crashes
  if (!job) {
    return (
      <View style={[JobDetailScreenStyles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ fontSize: 16, color: '#666' }}>Job details not available</Text>
        <TouchableOpacity 
          style={{ marginTop: 20, padding: 12, backgroundColor: '#007AFF', borderRadius: 8 }}
          onPress={() => navigation.goBack()}
        >
          <Text style={{ color: 'white', fontSize: 16 }}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={JobDetailScreenStyles.container}>
      {/* Main Job Info */}
      <View
        style={[
          JobDetailScreenStyles.headerCard,
          { flexDirection: 'row', alignItems: 'center' },
        ]}
      >
        <Image
          source={
            job.companyLogo
              ? { uri: job.companyLogo }
              : require('../assets/TW.png')
          }
          style={JobDetailScreenStyles.companyLogo}
          resizeMode="contain"
        />
        <View style={{ flex: 1, marginLeft: 16 }}>
          <Text
            style={JobDetailScreenStyles.title}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {job.jobTitle}
          </Text>
          <Text style={JobDetailScreenStyles.company}>{job.company}</Text>
          <Text style={JobDetailScreenStyles.location}>{job.location}</Text>
        </View>
      </View>
      <View style={JobDetailScreenStyles.metaContainer}>
        <Text style={JobDetailScreenStyles.meta}>
          {job.tradeName || 'N/A'} |{' '}
          {job.dependsOnExperience
            ? 'Depends on Experience'
            : job.minSalary !== undefined &&
              job?.maxSalary !== undefined &&
              job?.duration
            ? `${getFormattedCurrency(
                job.minSalary,
                job.duration,
              )} - ${getFormattedCurrency(job.maxSalary, job.duration)} ${
                job.duration
              }`
            : 'N/A'}{' '}
          | {job.createdAt ? formatTime(job.createdAt) : 'N/A'} |{' '}
          {job?.level?.length ? job.level.join(', ') : 'N/A'} |{' '}
          {job?.companySize || 'N/A'} |{' '}
          {job?.vibes?.length
            ? job.vibes.map(vibe => vibe?.name || '').filter(Boolean).join(', ')
            : 'N/A'}{' '}
          | {job.applicants || 0} Applicants
        </Text>
        <Text style={JobDetailScreenStyles.meta}>
          {job.education || 'N/A'} | {job.employees || 'N/A'} | {job.benefits?.length ? job.benefits.join(', ') : 'N/A'}
        </Text>
        <Text style={JobDetailScreenStyles.meta}>
          {job.alumni} | {job.apprenticeship}
        </Text>
      </View>
      
      {/* Easy Apply Button */}
      {/* <TouchableOpacity
        style={JobDetailScreenStyles.easyApplyButton}
        onPress={() => navigation.navigate('EasyApply')}
        accessibilityLabel="Easy Apply Button"
      >
        <Text style={JobDetailScreenStyles.easyApplyIcon}>⚡</Text>
        <Text style={JobDetailScreenStyles.easyApplyButtonText}>EASY APPLY</Text>
      </TouchableOpacity> */}
      {/* Job Summary */}
      <View style={JobDetailScreenStyles.section}>
        <Text style={JobDetailScreenStyles.meta2}>{job.jobListing || 'N/A'}</Text>
        <Text style={JobDetailScreenStyles.sectionTitle}>JOB SUMMARY</Text>
        {job.jobSummary ? (
          <RenderHTML
            contentWidth={useWindowDimensions().width}
            source={{ html: job.jobSummary }}
            baseStyle={JobDetailScreenStyles.body}
          />
        ) : (
          <Text style={JobDetailScreenStyles.body}>No job summary available</Text>
        )}
      </View>

      {/* Responsibilities */}
      <View style={JobDetailScreenStyles.section}>
        <Text style={JobDetailScreenStyles.sectionTitle}>
          KEY RESPONSIBILITIES & DAY-TO-DAY
        </Text>
        {job.jobResponsibilities ? (
          <RenderHTML
            contentWidth={useWindowDimensions().width}
            source={{ html: job.jobResponsibilities }}
            baseStyle={JobDetailScreenStyles.body}
          />
        ) : (
          <Text style={JobDetailScreenStyles.body}>No responsibilities information available</Text>
        )}
      </View>

      {/* Dream Job Match */}
      <View style={JobDetailScreenStyles.section}>
        <Text style={JobDetailScreenStyles.sectionTitle}>DREAM JOB MATCH</Text>
        <View style={JobDetailScreenStyles.dreamJobMatchContainer}>
          {returnJobDreamIteams(job)}
        </View>
      </View>

      {/* <View style={JobDetailScreenStyles.section}>
        <Text style={JobDetailScreenStyles.sectionTitle}>
          APPLICATION PROCESS
        </Text>
        <View style={JobDetailScreenStyles.applicationProcessContainer}>
          {job.applicationProcess.map((item, idx) => (
            <Text key={idx} style={JobDetailScreenStyles.applicationProcess}>
              {item}
            </Text>
          ))}
        </View>
      </View>

      <View style={JobDetailScreenStyles.consoleContainer}>
        <Text style={JobDetailScreenStyles.consoleTitle}>Console</Text>
        <Text style={JobDetailScreenStyles.consoleText}>
          {JSON.stringify(job, null, 2)}
        </Text>
      </View> */}
    </ScrollView>
  );
};

export default JobDetailScreen;
