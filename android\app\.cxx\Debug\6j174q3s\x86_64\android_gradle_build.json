{"buildFiles": ["D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\.cxx\\Debug\\6j174q3s\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\.cxx\\Debug\\6j174q3s\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "appmodules", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libreact_codegen_rnpicker.so", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\9be1b982dde0a83b606df121a311dc86\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_RNLocalizeSpec::@f3ba5298aff84514e205": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_RNLocalizeSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnpicker", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\9be1b982dde0a83b606df121a311dc86\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnscreens", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\9be1b982dde0a83b606df121a311dc86\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_safeareacontext", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\9be1b982dde0a83b606df121a311dc86\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}