{"name": "TradeWorks", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@formatjs/intl-datetimeformat": "^6.18.0", "@formatjs/intl-numberformat": "^8.15.4", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/checkbox": "^0.5.20", "@react-native-picker/picker": "^2.11.1", "@react-native/new-app-screen": "0.80.2", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.11.0", "intl-pluralrules": "^2.0.1", "react": "18.2.0", "react-native": "0.80.2", "react-native-dropdown-picker": "^5.4.6", "react-native-localize": "^3.5.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.13.1", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@types/jest": "^29.5.13", "@types/react": "^18.2.0", "@types/react-redux": "^7.1.34", "@types/react-test-renderer": "^18.2.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}