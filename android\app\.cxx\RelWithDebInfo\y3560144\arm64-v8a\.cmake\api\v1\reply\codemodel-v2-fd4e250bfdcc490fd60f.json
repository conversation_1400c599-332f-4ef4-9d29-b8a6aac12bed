{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-be897b134a506810cb55.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rnpicker_autolinked_build", "jsonFile": "directory-rnpicker_autolinked_build-RelWithDebInfo-7b491db6c020ac3d9094.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni", "targetIndexes": [3]}, {"build": "RNLocalizeSpec_autolinked_build", "jsonFile": "directory-RNLocalizeSpec_autolinked_build-RelWithDebInfo-bb8d84a18749765b50bc.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-a2e7567686442f3f11d1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-7f5f3fdabf4bd566e272.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-04e4faa865b61d751d1d.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNLocalizeSpec::@f3ba5298aff84514e205", "jsonFile": "target-react_codegen_RNLocalizeSpec-RelWithDebInfo-3d28cbbdc4ee1f3e573d.json", "name": "react_codegen_RNLocalizeSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-fb94ea280e2ac94f7d50.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516", "jsonFile": "target-react_codegen_rnpicker-RelWithDebInfo-db6933b1d4d5b050cb9c.json", "name": "react_codegen_rnpicker", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-ec002540fa1158591a7b.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-cb07e6546b6afdd484ab.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/RelWithDebInfo/y3560144/arm64-v8a", "source": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}