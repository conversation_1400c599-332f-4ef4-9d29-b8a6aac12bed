{"artifacts": [{"path": "D:/TradeWorks/Tradeworks-mobile/android/app/build/intermediates/cxx/Debug/6j174q3s/obj/arm64-v8a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_compile_reactnative_options", "target_include_directories"], "files": ["D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake", "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 64, "parent": 2}, {"command": 2, "file": 0, "line": 95, "parent": 2}, {"command": 2, "file": 0, "line": 81, "parent": 2}, {"command": 4, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 2, "line": 30, "parent": 6}, {"command": 3, "file": 2, "line": 36, "parent": 6}, {"command": 5, "file": 0, "line": 66, "parent": 2}, {"file": 3}, {"command": 5, "file": 3, "line": 77, "parent": 10}, {"file": 4}, {"command": 5, "file": 4, "line": 62, "parent": 12}, {"file": 5}, {"command": 5, "file": 5, "line": 85, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 7, "fragment": "-Wall"}, {"backtrace": 7, "fragment": "-Werror"}, {"backtrace": 7, "fragment": "-fexceptions"}, {"backtrace": 7, "fragment": "-frtti"}, {"backtrace": 7, "fragment": "-std=c++20"}, {"backtrace": 7, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 8, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 9, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 9, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 11, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni"}, {"backtrace": 13, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 15, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/9be1b982dde0a83b606df121a311dc86/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/f480fa9c767edb2ea9172ed7fbce011f/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/f480fa9c767edb2ea9172ed7fbce011f/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516"}, {"backtrace": 4, "id": "react_codegen_RNLocalizeSpec::@f3ba5298aff84514e205"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\arm64-v8a\\libreact_codegen_rnpicker.so", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6j174q3s\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\9be1b982dde0a83b606df121a311dc86\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f480fa9c767edb2ea9172ed7fbce011f\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/RNLocalizeSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/RNLocalizeSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/arm64-v8a/RNLocalizeSpec_autolinked_build/CMakeFiles/react_codegen_RNLocalizeSpec.dir/react/renderer/components/RNLocalizeSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}