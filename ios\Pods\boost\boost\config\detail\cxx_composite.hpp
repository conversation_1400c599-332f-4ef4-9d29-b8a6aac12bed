//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#if defined(BOOST_NO_ADL_BARRIER)\
   || defined(BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP)\
   || defined(BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS)\
   || defined(BOOST_NO_COMPLETE_VALUE_INITIALIZATION)\
   || defined(BOOST_NO_CTYPE_FUNCTIONS)\
   || defined(BOOST_NO_CV_SPECIALIZATIONS)\
   || defined(BOOST_NO_CV_VOID_SPECIALIZATIONS)\
   || defined(BOOST_NO_CWCHAR)\
   || defined(BOOST_NO_CWCTYPE)\
   || defined(BOOST_NO_DEPENDENT_NESTED_DERIVATIONS)\
   || defined(BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS)\
   || defined(BOOST_NO_EXCEPTIONS)\
   || defined(BOOST_NO_EXCEPTION_STD_NAMESPACE)\
   || defined(BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS)\
   || defined(BOOST_NO_FENV_H)\
   || defined(BOOST_NO_FUNCTION_TEMPLATE_ORDERING)\
   || defined(BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS)\
   || defined(BOOST_NO_INCLASS_MEMBER_INITIALIZATION)\
   || defined(BOOST_NO_INTEGRAL_INT64_T)\
   || defined(BOOST_NO_INTRINSIC_WCHAR_T)\
   || defined(BOOST_NO_IOSFWD)\
   || defined(BOOST_NO_IOSTREAM)\
   || defined(BOOST_NO_IS_ABSTRACT)\
   || defined(BOOST_NO_LIMITS)\
   || defined(BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS)\
   || defined(BOOST_NO_LONG_LONG)\
   || defined(BOOST_NO_LONG_LONG_NUMERIC_LIMITS)\
   || defined(BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS)\
   || defined(BOOST_NO_MEMBER_TEMPLATES)\
   || defined(BOOST_NO_MEMBER_TEMPLATE_FRIENDS)\
   || defined(BOOST_NO_MEMBER_TEMPLATE_KEYWORD)\
   || defined(BOOST_NO_NESTED_FRIENDSHIP)\
   || defined(BOOST_NO_OPERATORS_IN_NAMESPACE)\
   || defined(BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS)\
   || defined(BOOST_NO_POINTER_TO_MEMBER_CONST)\
   || defined(BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS)\
   || defined(BOOST_NO_PRIVATE_IN_AGGREGATE)\
   || defined(BOOST_NO_RESTRICT_REFERENCES)\
   || defined(BOOST_NO_RTTI)\
   || defined(BOOST_NO_SFINAE)\
   || defined(BOOST_NO_SFINAE_EXPR)\
   || defined(BOOST_NO_STDC_NAMESPACE)\
   || defined(BOOST_NO_STD_ALLOCATOR)\
   || defined(BOOST_NO_STD_DISTANCE)\
   || defined(BOOST_NO_STD_ITERATOR)\
   || defined(BOOST_NO_STD_ITERATOR_TRAITS)\
   || defined(BOOST_NO_STD_LOCALE)\
   || defined(BOOST_NO_STD_MESSAGES)\
   || defined(BOOST_NO_STD_MIN_MAX)\
   || defined(BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN)\
   || defined(BOOST_NO_STD_TYPEINFO)\
   || defined(BOOST_NO_STD_USE_FACET)\
   || defined(BOOST_NO_STD_WSTREAMBUF)\
   || defined(BOOST_NO_STD_WSTRING)\
   || defined(BOOST_NO_STRINGSTREAM)\
   || defined(BOOST_NO_TEMPLATED_IOSTREAMS)\
   || defined(BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS)\
   || defined(BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION)\
   || defined(BOOST_NO_TEMPLATE_TEMPLATES)\
   || defined(BOOST_NO_TWO_PHASE_NAME_LOOKUP)\
   || defined(BOOST_NO_TYPEID)\
   || defined(BOOST_NO_TYPENAME_WITH_CTOR)\
   || defined(BOOST_NO_UNREACHABLE_RETURN_DETECTION)\
   || defined(BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE)\
   || defined(BOOST_NO_USING_TEMPLATE)\
   || defined(BOOST_NO_VOID_RETURNS)
#    define BOOST_NO_CXX03
#endif

#if defined(BOOST_NO_CXX03)\
   || defined(BOOST_NO_CXX11_ADDRESSOF)\
   || defined(BOOST_NO_CXX11_ALIGNAS)\
   || defined(BOOST_NO_CXX11_ALIGNOF)\
   || defined(BOOST_NO_CXX11_ALLOCATOR)\
   || defined(BOOST_NO_CXX11_AUTO_DECLARATIONS)\
   || defined(BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS)\
   || defined(BOOST_NO_CXX11_CHAR16_T)\
   || defined(BOOST_NO_CXX11_CHAR32_T)\
   || defined(BOOST_NO_CXX11_CONSTEXPR)\
   || defined(BOOST_NO_CXX11_DECLTYPE)\
   || defined(BOOST_NO_CXX11_DECLTYPE_N3276)\
   || defined(BOOST_NO_CXX11_DEFAULTED_FUNCTIONS)\
   || defined(BOOST_NO_CXX11_DEFAULTED_MOVES)\
   || defined(BOOST_NO_CXX11_DELETED_FUNCTIONS)\
   || defined(BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS)\
   || defined(BOOST_NO_CXX11_EXTERN_TEMPLATE)\
   || defined(BOOST_NO_CXX11_FINAL)\
   || defined(BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS)\
   || defined(BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS)\
   || defined(BOOST_NO_CXX11_HDR_ARRAY)\
   || defined(BOOST_NO_CXX11_HDR_ATOMIC)\
   || defined(BOOST_NO_CXX11_HDR_CHRONO)\
   || defined(BOOST_NO_CXX11_HDR_CONDITION_VARIABLE)\
   || defined(BOOST_NO_CXX11_HDR_EXCEPTION)\
   || defined(BOOST_NO_CXX11_HDR_FORWARD_LIST)\
   || defined(BOOST_NO_CXX11_HDR_FUNCTIONAL)\
   || defined(BOOST_NO_CXX11_HDR_FUTURE)\
   || defined(BOOST_NO_CXX11_HDR_INITIALIZER_LIST)\
   || defined(BOOST_NO_CXX11_HDR_MUTEX)\
   || defined(BOOST_NO_CXX11_HDR_RANDOM)\
   || defined(BOOST_NO_CXX11_HDR_RATIO)\
   || defined(BOOST_NO_CXX11_HDR_REGEX)\
   || defined(BOOST_NO_CXX11_HDR_SYSTEM_ERROR)\
   || defined(BOOST_NO_CXX11_HDR_THREAD)\
   || defined(BOOST_NO_CXX11_HDR_TUPLE)\
   || defined(BOOST_NO_CXX11_HDR_TYPEINDEX)\
   || defined(BOOST_NO_CXX11_HDR_TYPE_TRAITS)\
   || defined(BOOST_NO_CXX11_HDR_UNORDERED_MAP)\
   || defined(BOOST_NO_CXX11_HDR_UNORDERED_SET)\
   || defined(BOOST_NO_CXX11_INLINE_NAMESPACES)\
   || defined(BOOST_NO_CXX11_LAMBDAS)\
   || defined(BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS)\
   || defined(BOOST_NO_CXX11_NOEXCEPT)\
   || defined(BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS)\
   || defined(BOOST_NO_CXX11_NULLPTR)\
   || defined(BOOST_NO_CXX11_NUMERIC_LIMITS)\
   || defined(BOOST_NO_CXX11_OVERRIDE)\
   || defined(BOOST_NO_CXX11_POINTER_TRAITS)\
   || defined(BOOST_NO_CXX11_RANGE_BASED_FOR)\
   || defined(BOOST_NO_CXX11_RAW_LITERALS)\
   || defined(BOOST_NO_CXX11_REF_QUALIFIERS)\
   || defined(BOOST_NO_CXX11_RVALUE_REFERENCES)\
   || defined(BOOST_NO_CXX11_SCOPED_ENUMS)\
   || defined(BOOST_NO_CXX11_SFINAE_EXPR)\
   || defined(BOOST_NO_CXX11_SMART_PTR)\
   || defined(BOOST_NO_CXX11_STATIC_ASSERT)\
   || defined(BOOST_NO_CXX11_STD_ALIGN)\
   || defined(BOOST_NO_CXX11_TEMPLATE_ALIASES)\
   || defined(BOOST_NO_CXX11_THREAD_LOCAL)\
   || defined(BOOST_NO_CXX11_TRAILING_RESULT_TYPES)\
   || defined(BOOST_NO_CXX11_UNICODE_LITERALS)\
   || defined(BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX)\
   || defined(BOOST_NO_CXX11_UNRESTRICTED_UNION)\
   || defined(BOOST_NO_CXX11_USER_DEFINED_LITERALS)\
   || defined(BOOST_NO_CXX11_VARIADIC_MACROS)\
   || defined(BOOST_NO_CXX11_VARIADIC_TEMPLATES)
#    define BOOST_NO_CXX11
#endif

#if defined(BOOST_NO_CXX11)\
   || defined(BOOST_NO_CXX14_AGGREGATE_NSDMI)\
   || defined(BOOST_NO_CXX14_BINARY_LITERALS)\
   || defined(BOOST_NO_CXX14_CONSTEXPR)\
   || defined(BOOST_NO_CXX14_DECLTYPE_AUTO)\
   || defined(BOOST_NO_CXX14_DIGIT_SEPARATORS)\
   || defined(BOOST_NO_CXX14_GENERIC_LAMBDAS)\
   || defined(BOOST_NO_CXX14_HDR_SHARED_MUTEX)\
   || defined(BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES)\
   || defined(BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION)\
   || defined(BOOST_NO_CXX14_STD_EXCHANGE)\
   || defined(BOOST_NO_CXX14_VARIABLE_TEMPLATES)
#    define BOOST_NO_CXX14
#endif

#if defined(BOOST_NO_CXX14)\
   || defined(BOOST_NO_CXX17_DEDUCTION_GUIDES)\
   || defined(BOOST_NO_CXX17_FOLD_EXPRESSIONS)\
   || defined(BOOST_NO_CXX17_HDR_ANY)\
   || defined(BOOST_NO_CXX17_HDR_CHARCONV)\
   || defined(BOOST_NO_CXX17_HDR_EXECUTION)\
   || defined(BOOST_NO_CXX17_HDR_FILESYSTEM)\
   || defined(BOOST_NO_CXX17_HDR_MEMORY_RESOURCE)\
   || defined(BOOST_NO_CXX17_HDR_OPTIONAL)\
   || defined(BOOST_NO_CXX17_HDR_STRING_VIEW)\
   || defined(BOOST_NO_CXX17_HDR_VARIANT)\
   || defined(BOOST_NO_CXX17_IF_CONSTEXPR)\
   || defined(BOOST_NO_CXX17_INLINE_VARIABLES)\
   || defined(BOOST_NO_CXX17_ITERATOR_TRAITS)\
   || defined(BOOST_NO_CXX17_STD_APPLY)\
   || defined(BOOST_NO_CXX17_STD_INVOKE)\
   || defined(BOOST_NO_CXX17_STRUCTURED_BINDINGS)
#    define BOOST_NO_CXX17
#endif

#if defined(BOOST_NO_CXX17)\
   || defined(BOOST_NO_CXX20_HDR_BARRIER)\
   || defined(BOOST_NO_CXX20_HDR_BIT)\
   || defined(BOOST_NO_CXX20_HDR_COMPARE)\
   || defined(BOOST_NO_CXX20_HDR_CONCEPTS)\
   || defined(BOOST_NO_CXX20_HDR_COROUTINE)\
   || defined(BOOST_NO_CXX20_HDR_FORMAT)\
   || defined(BOOST_NO_CXX20_HDR_LATCH)\
   || defined(BOOST_NO_CXX20_HDR_NUMBERS)\
   || defined(BOOST_NO_CXX20_HDR_RANGES)\
   || defined(BOOST_NO_CXX20_HDR_SEMAPHORE)\
   || defined(BOOST_NO_CXX20_HDR_SOURCE_LOCATION)\
   || defined(BOOST_NO_CXX20_HDR_SPAN)\
   || defined(BOOST_NO_CXX20_HDR_STOP_TOKEN)\
   || defined(BOOST_NO_CXX20_HDR_SYNCSTREAM)\
   || defined(BOOST_NO_CXX20_HDR_VERSION)
#    define BOOST_NO_CXX20
#endif

#if defined(BOOST_NO_CXX20)\
   || defined(BOOST_NO_CXX23_HDR_EXPECTED)\
   || defined(BOOST_NO_CXX23_HDR_FLAT_MAP)\
   || defined(BOOST_NO_CXX23_HDR_FLAT_SET)\
   || defined(BOOST_NO_CXX23_HDR_GENERATOR)\
   || defined(BOOST_NO_CXX23_HDR_MDSPAN)\
   || defined(BOOST_NO_CXX23_HDR_PRINT)\
   || defined(BOOST_NO_CXX23_HDR_SPANSTREAM)\
   || defined(BOOST_NO_CXX23_HDR_STACKTRACE)\
   || defined(BOOST_NO_CXX23_HDR_STDFLOAT)
#    define BOOST_NO_CXX23
#endif

