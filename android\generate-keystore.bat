@echo off
echo Generating Android Release Keystore for TradeWorks
echo ===================================================
echo.
echo This script will generate a keystore file for signing your release APK/AAB
echo You will be prompted for keystore and key passwords - REMEMBER THESE!
echo.
pause

set KEYSTORE_NAME=my-upload-key.keystore
set KEY_ALIAS=my-key-alias

echo Generating keystore...
keytool -genkeypair -v -storetype PKCS12 -keystore %KEYSTORE_NAME% -alias %KEY_ALIAS% -keyalg RSA -keysize 2048 -validity 10000

if %ERRORLEVEL% == 0 (
    echo.
    echo ===================================================
    echo SUCCESS! Keystore generated successfully.
    echo.
    echo File: %KEYSTORE_NAME%
    echo Alias: %KEY_ALIAS%
    echo.
    echo IMPORTANT: 
    echo 1. Keep this keystore file safe and backed up
    echo 2. Remember your passwords - you'll need them for every release
    echo 3. Update gradle.properties with your keystore details
    echo.
    echo Next steps:
    echo 1. Edit android/gradle.properties
    echo 2. Uncomment and set the MYAPP_UPLOAD_* properties
    echo 3. Run: cd android ^&^& ./gradlew assembleRelease
    echo.
) else (
    echo.
    echo ERROR: Failed to generate keystore
    echo Make sure Java keytool is installed and in your PATH
)

pause
