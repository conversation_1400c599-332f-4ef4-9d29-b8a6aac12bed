# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt:20 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerMeasurementsManager.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerState.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerMeasurementsManager.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerState.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/rnpicker.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/RNLocalizeSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/ComponentDescriptors.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/EventEmitters.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/Props.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/RNLocalizeSpecJSI-generated.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/ShadowNodes.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-localize/android/build/generated/source/codegen/jni/react/renderer/components/RNLocalizeSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:12 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:12 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:13 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:13 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:50 (file)
# input_SRC at D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "D:/TradeWorks/Tradeworks-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/TradeWorks/Tradeworks-mobile/android/app/.cxx/Debug/6j174q3s/armeabi-v7a/CMakeFiles/cmake.verify_globs")
endif()
