{"buildFiles": ["D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-localize\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\TradeWorks\\Tradeworks-mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\.cxx\\RelWithDebInfo\\y3560144\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\.cxx\\RelWithDebInfo\\y3560144\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "armeabi-v7a", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "armeabi-v7a", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "armeabi-v7a", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNLocalizeSpec::@f3ba5298aff84514e205": {"artifactName": "react_codegen_RNLocalizeSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"artifactName": "react_codegen_rnpicker", "abi": "armeabi-v7a", "output": "D:\\TradeWorks\\Tradeworks-mobile\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\y3560144\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3cec8c5b1a3c8047758b5cc888b1af12\\transformed\\react-android-0.80.2-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}}}