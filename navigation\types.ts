// Root navigation types for React Navigation

// Define JobData type here to avoid circular imports
export type JobData = {
  jobTitle: string;
  company: string;
  location: string;
  type: string;
  hours: string;
  experience: string;
  education: string;
  employees: string;
  benefits: string[];
  alumni: string;
  apprenticeship: string;
  dreamJobMatch: { label: string; match: boolean }[];
  applicationProcess: string[];
  companyLogo?: string;
  jobListing: string;
  jobSummary: string;
  jobResponsibilities: string;
  jobPostingDate: string;
  jobLevel: string[];
  companySize: string;
  vibes: { name: string }[];
  jobPerks: string[];
  companyInterest: string[];
  tradeName: string;
  dependsOnExperience: boolean;
  minSalary: number;
  maxSalary: number;
  duration: string;
  level: string[];
  applicants: number;
  createdAt: string;
  dreamJobInfo: {
    configValue: {
      isCompleted: boolean;
      userAttributes: string;
    };
  }[];
};

export type RootStackParamList = {
  SignIn: undefined;
  JoinUs: undefined;
  Location: undefined;
  Companion: undefined;
  JobListScreen: undefined;
  JobDetailScreen: { job: JobData };
  EasyApply: undefined;
  EditContactInfo: { from: string };
};
